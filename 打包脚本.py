import os
import subprocess
import sys
import shutil
from pathlib import Path

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("PyInstaller安装成功！")
    except subprocess.CalledProcessError:
        print("PyInstaller安装失败，请手动安装：pip install pyinstaller")
        return False
    return True

def create_spec_file():
    """创建PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['GLM识图提示词生成器.py'],
    pathex=[],
    binaries=[],
    datas=[],  # 不打包api_keys.txt和vdf文件
    hiddenimports=[
        'PIL._tkinter_finder',
        'tkinter',
        'tkinter.filedialog',
        'requests',
        'colorama',
        'tqdm',
        'PIL',
        'PIL.Image'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GLM识图提示词生成器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 保持控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''
    
    with open('GLM识图提示词生成器.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("已创建配置文件：GLM识图提示词生成器.spec")

def check_files():
    """检查必要文件是否存在"""
    required_files = ['GLM识图提示词生成器.py']
    missing_files = []

    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)

    if missing_files:
        print(f"缺少必要文件：{', '.join(missing_files)}")
        return False

    print("所有必要文件检查完成！")
    return True

def build_exe():
    """构建exe文件"""
    print("开始构建exe文件...")
    try:
        # 使用spec文件构建
        subprocess.check_call([
            'pyinstaller', 
            '--clean',  # 清理临时文件
            'GLM识图提示词生成器.spec'
        ])
        print("exe文件构建成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"构建失败：{e}")
        return False

def cleanup():
    """清理临时文件"""
    print("清理临时文件...")
    temp_dirs = ['build', '__pycache__']
    temp_files = ['GLM识图提示词生成器.spec']
    
    for dir_name in temp_dirs:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已删除临时目录：{dir_name}")
    
    for file_name in temp_files:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"已删除临时文件：{file_name}")

def main():
    print("="*60)
    print("GLM识图提示词生成器 - 打包工具")
    print("="*60)
    
    # 检查必要文件
    if not check_files():
        input("按回车键退出...")
        return
    
    # 安装PyInstaller
    if not install_pyinstaller():
        input("按回车键退出...")
        return
    
    # 创建配置文件
    create_spec_file()
    
    # 构建exe
    if build_exe():
        print("\n" + "="*60)
        print("打包完成！")
        print("exe文件位置：dist/GLM识图提示词生成器.exe")
        print("="*60)
        
        # 询问是否清理临时文件
        choice = input("是否清理临时文件？(y/n): ").lower()
        if choice == 'y':
            cleanup()
        
        print("\n使用说明：")
        print("1. 需要在exe文件同目录下放置api_keys.txt文件")
        print("2. api_keys.txt和key.vdf等敏感文件不会被打包到exe中")
        print("3. 直接运行exe文件即可使用")
        print("4. 生成的结果文件会保存在exe文件同目录下")
    else:
        print("打包失败！")
    
    input("按回车键退出...")

if __name__ == "__main__":
    main()