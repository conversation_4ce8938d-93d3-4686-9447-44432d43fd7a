# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['AI识图重命名工具.py'],
    pathex=[],
    binaries=[],
    datas=[],  # 不打包api_keys.txt等敏感文件
    hiddenimports=[
        'PIL._tkinter_finder',
        'tkinter',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'requests',
        'colorama',
        'tqdm',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'concurrent.futures',
        'threading',
        'queue',
        'json',
        'logging',
        'base64',
        'io',
        're',
        'datetime',
        'warnings'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI识图重命名工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 保持控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
